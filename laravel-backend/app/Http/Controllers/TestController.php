<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class TestController extends Controller
{
    /**
     * Display a test page with S3 image
     */
    public function testImage()
    {
        $imageUrl = 'https://laravelbucket2025.s3.ap-southeast-1.amazonaws.com/public/logo.jpg';
        
        return response()->json([
            'success' => true,
            'image_url' => $imageUrl,
            'html' => '<img src="' . $imageUrl . '" alt="Test Image" style="max-width: 500px;" />'
        ]);
    }

    /**
     * Display a simple HTML page with the image
     */
    public function testImagePage()
    {
        $imageUrl = 'https://laravelbucket2025.s3.ap-southeast-1.amazonaws.com/public/logo.jpg';
        
        $html = '<!DOCTYPE html>
<html>
<head>
    <title>S3 Image Test</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .image-container { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        img { max-width: 100%; height: auto; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <div class="container">
        <h1>S3 Image Display Test</h1>
        
        <div class="image-container">
            <h3>Direct Image Display:</h3>
            <p>Image URL: <a href="' . $imageUrl . '" target="_blank">' . $imageUrl . '</a></p>
            <img src="' . $imageUrl . '" alt="S3 Test Image" onload="console.log(\'Image loaded successfully\')" onerror="console.error(\'Image failed to load\'); this.style.border=\'2px solid red\';" />
        </div>
        
        <div class="image-container">
            <h3>Image with CORS Headers:</h3>
            <img src="' . $imageUrl . '" alt="S3 Test Image with CORS" crossorigin="anonymous" onload="console.log(\'CORS Image loaded successfully\')" onerror="console.error(\'CORS Image failed to load\'); this.style.border=\'2px solid red\';" />
        </div>
        
        <div class="image-container">
            <h3>Background Image:</h3>
            <div style="width: 200px; height: 200px; background-image: url(\'' . $imageUrl . '\'); background-size: contain; background-repeat: no-repeat; background-position: center; border: 1px solid #ccc;"></div>
        </div>
        
        <script>
            console.log("Testing image load from: ' . $imageUrl . '");
            
            // Test fetch
            fetch("' . $imageUrl . '", { mode: "no-cors" })
                .then(() => console.log("Fetch successful"))
                .catch(err => console.error("Fetch failed:", err));
        </script>
    </div>
</body>
</html>';

        return response($html)->header('Content-Type', 'text/html');
    }
}
